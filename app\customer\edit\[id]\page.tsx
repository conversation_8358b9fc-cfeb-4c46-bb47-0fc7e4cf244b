"use client"

import { useEffect, useState, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import JC_FormTablet, { JC_FormTabletModel } from "../../../components/JC_FormTablet/JC_FormTablet";
import JC_Spinner from "../../../components/JC_Spinner/JC_Spinner";
import { PropertyModel } from "../../../models/Property";
import { O_BuildingTypeModel } from "../../../models/O_BuildingType";
import { O_OrientationModel } from "../../../models/O_Orientation";
import { O_NumBedroomsModel } from "../../../models/O_NumBedrooms";
import { O_StoreysModel } from "../../../models/O_Storeys";
import { O_FurnishedModel } from "../../../models/O_Furnished";
import { O_OccupiedModel } from "../../../models/O_Occupied";
import { O_CompanyStrataTitleModel } from "../../../models/O_CompanyStrataTitle";
import { O_FloorModel } from "../../../models/O_Floor";
import { O_OtherBuildingElementsModel } from "../../../models/O_OtherBuildingElements";
import { O_OtherTimberBldgElementsModel } from "../../../models/O_OtherTimberBldgElements";
import { O_RoofModel } from "../../../models/O_Roof";
import { O_WallsModel } from "../../../models/O_Walls";
import { O_WeatherModel } from "../../../models/O_Weather";
import { FieldTypeEnum } from "../../../enums/FieldType";
import { JC_FieldOption } from "../../../models/ComponentModels/JC_FieldOption";
import { JC_Utils } from "@/app/Utils";
import { LocalStorageKeyEnum } from "../../../enums/LocalStorageKey";

export default function CustomerEditPage() {
    const router = useRouter();
    const params = useParams();
    const customerId = params.id as string;
    const isNewRecord = customerId === "new";

    // - STATE - //
    const [initialised, setInitialised] = useState<boolean>(false);
    const [currentCustomer, setCurrentCustomer] = useState<PropertyModel>(new PropertyModel());
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // Option lists
    const [buildingTypeOptions, setBuildingTypeOptions] = useState<JC_FieldOption[]>([]);
    const [companyStrataTitleOptions, setCompanyStrataTitleOptions] = useState<JC_FieldOption[]>([]);
    const [orientationOptions, setOrientationOptions] = useState<JC_FieldOption[]>([]);
    const [numBedroomsOptions, setNumBedroomsOptions] = useState<JC_FieldOption[]>([]);
    const [storeysOptions, setStoreysOptions] = useState<JC_FieldOption[]>([]);
    const [furnishedOptions, setFurnishedOptions] = useState<JC_FieldOption[]>([]);
    const [occupiedOptions, setOccupiedOptions] = useState<JC_FieldOption[]>([]);
    const [floorOptions, setFloorOptions] = useState<JC_FieldOption[]>([]);
    const [otherBuildingElementsOptions, setOtherBuildingElementsOptions] = useState<JC_FieldOption[]>([]);
    const [otherTimberBldgElementsOptions, setOtherTimberBldgElementsOptions] = useState<JC_FieldOption[]>([]);
    const [roofOptions, setRoofOptions] = useState<JC_FieldOption[]>([]);
    const [wallsOptions, setWallsOptions] = useState<JC_FieldOption[]>([]);
    const [weatherOptions, setWeatherOptions] = useState<JC_FieldOption[]>([]);

    // Convert option models to JC_FieldOption format
    const convertToFieldOptions = (models: any[]): JC_FieldOption[] => {
        return models.map(model => ({
            OptionId: model.Code,
            Label: model.Name,
            Selected: false
        }));
    };

    // Load options data
    const loadOptions = useCallback(async () => {
        try {
            setIsLoading(true);

            const [
                buildingTypes,
                companyStrataTitles,
                orientations,
                numBedrooms,
                storeys,
                furnished,
                occupied,
                floors,
                otherBuildingElements,
                otherTimberBldgElements,
                roofs,
                walls,
                weather
            ] = await Promise.all([
                O_BuildingTypeModel.GetList(),
                O_CompanyStrataTitleModel.GetList(),
                O_OrientationModel.GetList(),
                O_NumBedroomsModel.GetList(),
                O_StoreysModel.GetList(),
                O_FurnishedModel.GetList(),
                O_OccupiedModel.GetList(),
                O_FloorModel.GetList(),
                O_OtherBuildingElementsModel.GetList(),
                O_OtherTimberBldgElementsModel.GetList(),
                O_RoofModel.GetList(),
                O_WallsModel.GetList(),
                O_WeatherModel.GetList()
            ]);

            setBuildingTypeOptions(convertToFieldOptions(buildingTypes || []));
            setCompanyStrataTitleOptions(convertToFieldOptions(companyStrataTitles || []));
            setOrientationOptions(convertToFieldOptions(orientations || []));
            setNumBedroomsOptions(convertToFieldOptions(numBedrooms || []));
            setStoreysOptions(convertToFieldOptions(storeys || []));
            setFurnishedOptions(convertToFieldOptions(furnished || []));
            setOccupiedOptions(convertToFieldOptions(occupied || []));
            setFloorOptions(convertToFieldOptions(floors || []));
            setOtherBuildingElementsOptions(convertToFieldOptions(otherBuildingElements || []));
            setOtherTimberBldgElementsOptions(convertToFieldOptions(otherTimberBldgElements || []));
            setRoofOptions(convertToFieldOptions(roofs || []));
            setWallsOptions(convertToFieldOptions(walls || []));
            setWeatherOptions(convertToFieldOptions(weather || []));

        } catch (error) {
            console.error('Error loading options:', error);
        } finally {
            setIsLoading(false);
            setInitialised(true);
        }
    }, []);

    // Load customer data
    const loadCustomer = useCallback(async () => {
        if (!isNewRecord) {
            try {
                setIsLoading(true);
                const customer = await PropertyModel.Get(customerId);
                if (customer) {
                    setCurrentCustomer(customer);
                }
            } catch (error) {
                console.error('Error loading customer:', error);
            } finally {
                setIsLoading(false);
            }
        }
    }, [customerId, isNewRecord]);

    // Load data on mount
    useEffect(() => {
        loadOptions();
        loadCustomer();
    }, [loadOptions, loadCustomer]);

    // Handle form submission
    const handleSubmit = async () => {
        try {
            setIsLoading(true);

            let response;
            if (isNewRecord) {
                response = await PropertyModel.Create(currentCustomer);
                if (response) {
                    JC_Utils.showToastSuccess("Customer created successfully!");
                }
            } else {
                response = await PropertyModel.Update(currentCustomer);
                if (response) {
                    JC_Utils.showToastSuccess("Customer updated successfully!");
                }
            }

            if (response) {
                router.push('/customer');
            }
        } catch (error) {
            console.error('Error saving customer:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Handle "Go to Property" button click
    const handleGoToProperty = () => {
        if (!isNewRecord && currentCustomer.Id) {
            localStorage.setItem(LocalStorageKeyEnum.JC_SelectedCustomer, currentCustomer.Id);
            router.push('/property');
        }
    };

    // Create the form tablet model
    const formTabletModel: JC_FormTabletModel = {
        headerLabel: `${isNewRecord ? "Create" : "Edit"} Customer${currentCustomer.Address ? ` - ${currentCustomer.Address}` : ""}`,
        leftPaneHeader: "Customer Fields",
        backButtonLink: "/customer",
        sections: [
            {
                Heading: "Customer Details",
                Fields: [
                    {
                        inputId: "address",
                        type: FieldTypeEnum.Text,
                        label: "Address",
                        value: currentCustomer.Address || "",
                        onChange: (newValue) => setCurrentCustomer(prev => ({ ...prev, Address: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Address is required." : ""
                    }
                ]
            }
        ],
        submitButtonText: isNewRecord ? "Create" : "Save",
        onSubmit: handleSubmit,
        isLoading: isLoading,
        additionalFooterButtons: !isNewRecord && currentCustomer.Id ? [
            {
                text: "Go to Property",
                onClick: handleGoToProperty
            }
        ] : undefined
    };

    // - RENDER - //
    return !initialised
        ? (<JC_Spinner isPageBody />)
        : (
            <JC_FormTablet model={formTabletModel} />
        );
}
